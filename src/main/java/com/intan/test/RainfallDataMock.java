package com.intan.test;

import com.intan.test.bean.HourRainfall;

import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.LocalTime;
import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR>
 */
public class RainfallDataMock {

    public List<HourRainfall> mockData() {
        List<HourRainfall> list = new ArrayList<>();
        HourRainfall hourRainfall = new HourRainfall();
        hourRainfall.setEquipId(1L);
        hourRainfall.setHour(LocalDateTime.of(LocalDate.now(), LocalTime.of(9, 0, 0)));
        hourRainfall.setRainfall(2.0f);
        list.add(hourRainfall);

        hourRainfall = new HourRainfall();
        hourRainfall.setEquipId(1L);
        hourRainfall.setHour(LocalDateTime.of(LocalDate.now(), LocalTime.of(10, 0, 0)));
        hourRainfall.setRainfall(0.0f);
        list.add(hourRainfall);

        hourRainfall = new HourRainfall();
        hourRainfall.setEquipId(1L);
        hourRainfall.setHour(LocalDateTime.of(LocalDate.now(), LocalTime.of(11, 0, 0)));
        hourRainfall.setRainfall(0.0f);
        list.add(hourRainfall);

        hourRainfall = new HourRainfall();
        hourRainfall.setEquipId(1L);
        hourRainfall.setHour(LocalDateTime.of(LocalDate.now(), LocalTime.of(12, 0, 0)));
        hourRainfall.setRainfall(0.0f);

        hourRainfall = new HourRainfall();
        hourRainfall.setEquipId(2L);
        hourRainfall.setHour(LocalDateTime.of(LocalDate.now(), LocalTime.of(9, 0, 0)));
        hourRainfall.setRainfall(0.0f);
        list.add(hourRainfall);

        hourRainfall = new HourRainfall();
        hourRainfall.setEquipId(2L);
        hourRainfall.setHour(LocalDateTime.of(LocalDate.now(), LocalTime.of(10, 0, 0)));
        hourRainfall.setRainfall(2.0f);
        list.add(hourRainfall);

        hourRainfall = new HourRainfall();
        hourRainfall.setEquipId(2L);
        hourRainfall.setHour(LocalDateTime.of(LocalDate.now(), LocalTime.of(11, 0, 0)));
        hourRainfall.setRainfall(0.0f);
        list.add(hourRainfall);

        hourRainfall = new HourRainfall();
        hourRainfall.setEquipId(2L);
        hourRainfall.setHour(LocalDateTime.of(LocalDate.now(), LocalTime.of(12, 0, 0)));
        hourRainfall.setRainfall(2.0f);

        return list;
    }
}
