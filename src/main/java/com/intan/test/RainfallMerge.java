package com.intan.test;

import com.intan.test.bean.HourRainfall;
import com.intan.test.utils.CollectionUtils;

import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 */
public class RainfallMerge {

    private static final DateTimeFormatter DATE_FORMATTER = DateTimeFormatter.ofPattern("yyyy-MM-dd");
    private static final DateTimeFormatter TIME_FORMATTER = DateTimeFormatter.ofPattern("HH:mm:ss");

    public static void main(String[] args) {
        RainfallDataMock mock = new RainfallDataMock();
        List<HourRainfall> list = mock.mockData();

        // 从list中获取所有设备连续降雨的时间段，返回一个List，数据格式为：第几次降雨、开始时间-结束时间
        List<String> result = RainfallMerge.merge(list);
        System.out.println(result);
    }

    /**
     * 合并所有设备的连续降雨时间段
     *
     * @param rainfallList 降雨数据列表
     * @return 合并后的降雨时间段列表，格式为：第几次降雨、开始时间-结束时间
     */
    public static List<String> merge(List<HourRainfall> rainfallList) {
        if (CollectionUtils.isAnyEmpty(rainfallList)) {
            return Collections.emptyList();
        }

        // 提取所有有降雨的时间点（所有设备的并集）
        List<LocalDateTime> rainfallTimes = rainfallList.stream()
                .filter(r -> r.getRainfall() > 0)
                .map(HourRainfall::getHour)
                .distinct()
                .sorted()
                .collect(Collectors.toList());

        // 找出连续的降雨时间段
        List<RainfallPeriod> periods = findContinuousRainfallPeriods(rainfallTimes);

        // 格式化结果
        List<String> result = new ArrayList<>();
        for (int i = 0; i < periods.size(); i++) {
            RainfallPeriod period = periods.get(i);
            String formattedResult = String.format("[%d,%s %s-%s]",
                    i + 1,
                    period.startTime().format(DATE_FORMATTER),
                    period.startTime().format(TIME_FORMATTER),
                    period.endTime().format(TIME_FORMATTER));
            result.add(formattedResult);
        }

        return result;
    }

    /**
     * 查找连续降雨的时间段
     */
    private static List<RainfallPeriod> findContinuousRainfallPeriods(List<LocalDateTime> sortedTimes) {
        List<RainfallPeriod> periods = new ArrayList<>();
        if (sortedTimes.isEmpty()) {
            return periods;
        }

        // 初始化第一个降雨时间段
        LocalDateTime startTime = sortedTimes.get(0);
        LocalDateTime endTime = startTime;

        for (int i = 1; i < sortedTimes.size(); i++) {
            LocalDateTime currentTime = sortedTimes.get(i);

            // 如果当前时间与前一个时间点相差不超过2小时，则视为连续降雨
            if (isConsecutiveHour(endTime, currentTime)) {
                // 更新结束时间
                endTime = currentTime;
            } else {
                // 添加一个完整的降雨时间段
                periods.add(new RainfallPeriod(startTime, endTime));

                // 开始新的时间段
                startTime = currentTime;
                endTime = currentTime;
            }
        }

        // 添加最后一个时间段
        periods.add(new RainfallPeriod(startTime, endTime));

        return periods;
    }

    /**
     * 判断两个时间是否连续（相差不超过2小时）
     * 注意：每个时间点的降雨数据代表的是前一小时的降雨量
     */
    private static boolean isConsecutiveHour(LocalDateTime time1, LocalDateTime time2) {
        // 计算两个时间点之间的小时差
        long hoursDifference = Math.abs(time1.until(time2, java.time.temporal.ChronoUnit.HOURS));

        // 考虑到每个时间点代表前一小时的降雨，如果两个时间点相差不超过2小时，则视为连续降雨
        return hoursDifference <= 2;
    }

    /**
     * 降雨时间段类
     */
    private record RainfallPeriod(LocalDateTime startTime, LocalDateTime endTime) {
    }
}